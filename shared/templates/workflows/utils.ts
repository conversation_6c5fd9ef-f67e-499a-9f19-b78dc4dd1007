// Utility functions for workflow templates
import { winbackTemplate, earlyCheckInUpsellTemplate, lateCheckOutUpsellTemplate, preStayGapNightUpsellTemplate, postStayExtraNightUpsellTemplate } from './index'


export function getWorkflowFromTemplate(name: string) {
    console.log('getting workflow template: ', name)
    switch (name) {
        case 'Inquiry Winback':
            return JSON.parse(JSON.stringify(winbackTemplate))
        case 'Early Check In Upsell':
            return JSON.parse(JSON.stringify(earlyCheckInUpsellTemplate))
        case 'Late Check Out Upsell':
            return JSON.parse(JSON.stringify(lateCheckOutUpsellTemplate))
        case 'Pre Stay Extra Night Upsell':
            return JSON.parse(JSON.stringify(preStayGapNightUpsellTemplate))
        case 'Post Stay Extra Night Upsell':
            return JSON.parse(JSON.stringify(postStayExtraNightUpsellTemplate))
        default:
            throw new Error('Unknown template name')
    }
}

// Step management logic
export function canAddStep(workflow: any) {
    // Only allow more than one step for winback
    return workflow.type === 'winback'
}

export function canAddStepByType(workflowType: string, currentStepCount: number) {
    // For upsell workflows, only allow 1 message step
    if (workflowType === 'upsell') {
        return currentStepCount === 0
    }
    // For winback/recovery workflows, allow multiple steps
    return workflowType === 'recovery' || workflowType === 'winback'
}

export function addMessageStep(workflow: any) {
    if (workflow.type === 'upsell') {
        // Only one step allowed for upsell
        return workflow
    }
    // For winback, add a delay and a message step at the end
    const stepKeys = Object.keys(workflow.steps).map(Number)
    const lastStep = Math.max(...stepKeys)
    const nextDelay = lastStep + 1
    const nextMessage = lastStep + 2
    workflow.steps[nextDelay] = { type: 'delay', duration: '1d' }
    workflow.steps[nextMessage] = {
        type: 'message',
        subject: '',
        body: ''
    }
    return workflow
}

export function deleteLastMessageStep(workflow: any) {
    if (workflow.type === 'upsell') {
        // Only one step, do nothing
        return workflow
    }
    // For winback, remove the last delay and message step pair
    const stepKeys = Object.keys(workflow.steps).map(Number)
    if (stepKeys.length < 2) return workflow
    const lastStep = Math.max(...stepKeys)
    const secondLastStep = lastStep - 1
    delete workflow.steps[lastStep]
    delete workflow.steps[secondLastStep]
    return workflow
} 