// Simple test to verify workflow step restrictions
import { canAddStepByType } from './shared/templates/workflows/utils.js'

console.log('Testing workflow step restrictions...')

// Test upsell workflow restrictions
console.log('\n=== Upsell Workflow Tests ===')
console.log('Can add step to upsell with 0 steps:', canAddStepByType('upsell', 0)) // Should be true
console.log('Can add step to upsell with 1 step:', canAddStepByType('upsell', 1)) // Should be false
console.log('Can add step to upsell with 2 steps:', canAddStepByType('upsell', 2)) // Should be false

// Test recovery/winback workflow restrictions
console.log('\n=== Recovery/Winback Workflow Tests ===')
console.log('Can add step to recovery with 0 steps:', canAddStepByType('recovery', 0)) // Should be true
console.log('Can add step to recovery with 1 step:', canAddStepByType('recovery', 1)) // Should be true
console.log('Can add step to recovery with 5 steps:', canAddStepByType('recovery', 5)) // Should be true

console.log('Can add step to winback with 0 steps:', canAddStepByType('winback', 0)) // Should be true
console.log('Can add step to winback with 1 step:', canAddStepByType('winback', 1)) // Should be true
console.log('Can add step to winback with 5 steps:', canAddStepByType('winback', 5)) // Should be true

console.log('\n✅ All tests completed!')
